const t = [
  {
    type: 'Feature',
    id: 'n40889936',
    geometry: { type: 'Point', coordinates: [37.532389, 55.8161292] },
    properties: {
      amenity: 'fuel',
      brand: 'Лукойл',
      'brand:wikidata': 'Q329347',
      'brand:wikipedia': 'ru:Лукой<PERSON>',
      'contact:email': '<EMAIL>',
      'contact:phone': '****** 4582018;****** 1000911',
      'contact:website': 'https://lukoil.ru/',
      'fuel:diesel': 'yes',
      'fuel:octane_92': 'yes',
      'fuel:octane_95': 'yes',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      opening_hours: '24/7',
      'payment:cards': 'yes',
      ref: '77747',
    },
  },
  {
    type: 'Feature',
    id: 'n40901233',
    geometry: { type: 'Point', coordinates: [37.5036492, 55.8363662] },
    properties: {
      'addr:housenumber': '24',
      'addr:street': 'Выборгская улица',
      amenity: 'fuel',
      brand: 'Газпром нефть',
      description: 'Заправка-автомат',
      'fuel:diesel': 'yes',
      'fuel:octane_92': 'yes',
      'fuel:octane_95': 'yes',
      name: 'Газпромнефть',
      opening_hours: '24/7',
      operator: 'ОАО "Газпромнефть-Центр"',
      'payment:cards': 'yes',
      ref: '142',
      website: 'https://www.gazprom-neft.ru/',
    },
  },
  {
    type: 'Feature',
    id: 'n40903852',
    geometry: { type: 'Point', coordinates: [37.535824, 55.8504727] },
    properties: {
      amenity: 'fuel',
      brand: 'Лукойл',
      'brand:en': 'Lukoil',
      'brand:ru': 'Лукойл',
      'brand:wikidata': 'Q329347',
      'contact:email': '<EMAIL>',
      'contact:phone': '****** 1000911',
      'contact:website': 'https://lukoil.ru/',
      'fuel:diesel': 'yes',
      'fuel:lpg': 'no',
      'fuel:octane': 'no;yes',
      name: 'Лукойл',
      'name:en': 'Lukoil',
      opening_hours: '24/7',
      operator: 'ООО "ЛУКОЙЛ-Центрнефтепродукт"',
      'payment:cards': 'yes',
      ref: '538',
    },
  },
]
