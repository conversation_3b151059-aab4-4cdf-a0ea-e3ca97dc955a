import { spawn } from 'node:child_process'

import { type OsmFeatureValueObject } from './osm-feature.value-object'
// import * as readline from 'readline'; // Optional, but good for line-based processing if not using RS

// Interface for your expected GeoJSON Feature (adjust properties as needed)

export async function streamPbfReader(
  pbfFilePath: string,
  onFeature: (feature: OsmFeatureValueObject) => void,
): Promise<{ terminate: () => void }> {
  console.log(`Starting Osmium export stream for: ${pbfFilePath}`)

  const command = 'osmium'
  const args = [
    'export',
    pbfFilePath,
    '--output-format=geojsonseq', // Recommended streaming format
    // '--output-format=jsonseq', // Alternative if you need Osmium's json format per object
    '--progress',
    '--show-errors',
    '--add-unique-id=type_id',
    // '-o',
    // 'extracted.json'
  ]

  const osmiumProcess = spawn(command, args, {
    stdio: ['ignore', 'pipe', 'pipe'], // stdin, stdout, stderr
  })

  // Add termination function
  const terminate = () => {
    if (osmiumProcess) {
      osmiumProcess.kill() // This sends SIGTERM by default
      console.log('Osmium process terminated')
    }
  }

  let recordBuffer = ''
  const recordSeparator = '\x1E' // ASCII Record Separator used by geojsonseq/jsonseq

  // --- Handle stdout (the GeoJSONseq data) ---
  osmiumProcess.stdout.setEncoding('utf8')
  osmiumProcess.stdout.on('data', (chunk: string) => {
    recordBuffer += chunk

    // Process buffer line by line based on the record separator
    let rsIndex
    while ((rsIndex = recordBuffer.indexOf(recordSeparator)) !== -1) {
      const rawRecord = recordBuffer.substring(0, rsIndex)
      recordBuffer = recordBuffer.substring(rsIndex + 1) // Keep the rest

      if (rawRecord.trim()) {
        // Avoid processing empty strings
        try {
          const feature: OsmFeatureValueObject = JSON.parse(rawRecord)

          // Do something more complex:
          // - Write to a database
          // - Send to another service
          // - Perform calculations
          // Be mindful of potential backpressure if your processing is slow
          onFeature(feature)
        } catch (parseError) {
          console.error('Error parsing JSON record:', parseError)
          console.error(
            'Problematic record snippet:',
            rawRecord.substring(0, 200),
          ) // Log snippet
          terminate()
          return
        }
      }
    }
  })

  osmiumProcess.stdout.on('end', () => {
    console.log('Osmium stdout stream ended.')
    // Process any remaining data in the buffer (should normally be empty if data ends with RS)
    if (recordBuffer.trim()) {
      try {
        const feature: OsmFeatureValueObject = JSON.parse(recordBuffer)
        onFeature(feature)
      } catch (parseError) {
        console.error('Error parsing final JSON record:', parseError)
        console.error(
          'Problematic record snippet:',
          recordBuffer.substring(0, 200),
        )
      }
    }
    recordBuffer = '' // Clear buffer
  })

  // --- Handle stderr (progress, errors from Osmium) ---
  osmiumProcess.stderr.setEncoding('utf8')
  osmiumProcess.stderr.on('data', (data: string) => {
    // Show Osmium's progress and errors directly
    process.stderr.write(`Osmium stderr: ${data}`)
  })

  // --- Handle process exit ---
  return new Promise((resolve, reject) => {
    osmiumProcess.on('close', (code) => {
      if (code === 0) {
        console.log('Osmium process finished successfully.')
        resolve({ terminate })
      } else {
        console.error(`Osmium process exited with code ${code}`)
        reject(new Error(`Osmium process exited with code ${code}`))
      }
    })

    osmiumProcess.on('error', (err) => {
      console.error('Failed to start Osmium process:', err)
      reject(err)
    })
  })
}

// // --- Your function to handle each parsed feature ---
// let featureCounter = 0;
// function processFeature(feature: OsmFeature) {
//   featureCounter++;
//   if (featureCounter % 10000 === 0) { // Log every 10000 features
//     console.log(`Processed ${featureCounter} features... Last: ${feature.properties['@unique_id']}`);
//   }
//   // Example: Add filtering based on properties
//   // if (feature.properties.amenity === 'restaurant') {
//   //     console.log(`Found restaurant: ${feature.properties.name || feature.properties['@id']}`);
//   // }
// }

// Usage example:
// const reader = await streamPbfReader(filePath, onFeature);
// // When you need to terminate:
// reader.terminate();
