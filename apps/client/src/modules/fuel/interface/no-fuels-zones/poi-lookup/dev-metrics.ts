type NumberMetrics = 'poiFindCall' | 'totalPoly' | 'maxCells' | 'currentCells'

export class DevMetrics {
  metrics: Record<NumberMetrics, number> = {
    poiFindCall: 0,
    totalPoly: 0,
    maxCells: 0,
    currentCells: 0,
  }

  reset(key?: NumberMetrics): void {
    if (key) {
      this.metrics[key] = 0
      return
    }
    this.metrics.poiFindCall = 0
    this.metrics.totalPoly = 0
    this.metrics.maxCells = 0
    this.metrics.currentCells = 0
  }

  inc(name: NumberMetrics): void {
    this.metrics[name]++
  }

  set(name: NumberMetrics, value: number): void {
    this.metrics[name] = value
  }
}
