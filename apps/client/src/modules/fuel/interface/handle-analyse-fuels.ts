import { Inject } from '@repo/service'

import { PolylinesStore } from '@wp-client/map-polyline/interface/polylines.store'
import { RoutesStore } from '@wp-client/user-route/interface/routes.store'
import { getCurrentRouteVehicles } from '@wp-client/user-vehicle/interface/get-current-route-vehicles'
import { WaypointsStore } from '@wp-client/waypoints/interface/waypoints.store'

import { analyseFuelsAdapter } from '../infra/analyse-fuels.adapter'

import { VehicleFuelsStore } from './vehicle-fuels.store'

export async function handleAnalyseFuels() {
  const wpStore = Inject(WaypointsStore)
  const polyStore = Inject(PolylinesStore)
  const vehicleFuels = Inject(VehicleFuelsStore)
  const vehicles = getCurrentRouteVehicles()
  const waypoints = wpStore.waypointList
  if (waypoints.length < 2) return
  if (vehicles.length === 0) return

  vehicleFuels.state.start()
  const routesStore = Inject(RoutesStore)
  const currentRoute = routesStore.currentRoute

  const data = await analyseFuelsAdapter({
    waypoints: wpStore.waypointList.map((wp) => {
      const node = polyStore.getPolyNodeByWpId(wp.id)
      const path = node?.polyNode ? node.polyNode.sPath || '' : ''
      return {
        wpId: wp.id,
        point: wp.latLng,
        path,
        mode: wp.mode,
      }
    }),
    vehicles,
    isRound: false,
  })

  const afterCurrentRoute = routesStore.currentRoute

  if (afterCurrentRoute?.id !== currentRoute?.id) return

  vehicleFuels.state.setResult(data)
  vehicleFuels.state.finish()
}
