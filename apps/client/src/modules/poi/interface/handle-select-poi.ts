import { Inject } from '@repo/service'

import { PoiCluster } from './poi-cluster/poi-cluster'
import { PoiStore } from './poi.store'

type Args = {
  id: string
  clusterIndex: number
  isFit?: boolean
  isToggle?: boolean
}

export function handleSelectPoi(opt: Args) {
  const poiStore = Inject(PoiStore)
  const cluster = Inject(PoiCluster)

  if (
    poiStore.selectedPoiMeta?.clusterIndex !== undefined &&
    poiStore.selectedPoiMeta.clusterIndex !== opt.clusterIndex
  ) {
    cluster.selectMarkerByIndex(poiStore.selectedPoiMeta.clusterIndex, false)
  }

  if (poiStore.selectedPoiMeta?.id === opt.id && opt.isToggle) {
    poiStore.setSelectedId(undefined)
    cluster.selectMarkerByIndex(opt.clusterIndex, false)
    return
  }
  poiStore.setSelectedId({
    id: opt.id,
    clusterIndex: opt.clusterIndex,
  })
  cluster.selectMarkerByIndex(opt.clusterIndex, true)
}
