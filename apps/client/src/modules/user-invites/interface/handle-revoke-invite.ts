import { isErr } from '@repo/result'
import { Inject } from '@repo/service'

import { revokeInviteAdapter } from '../infra/revoke-invite.adapter'

import { UserInvitesStore } from './user-invites.store'

export async function handleRevokeInvite(inviteUuid: string): Promise<void> {
  const res = await revokeInviteAdapter(inviteUuid)
  if (isErr(res)) {
    return
  }

  const store = Inject(UserInvitesStore)
  store.removeInvite(inviteUuid)
}
