import { observer } from '@repo/service'

import { Spinner, Swap, useInitialLoad } from '@wp/ui-kit'
import { useInject } from '@wp-client/service/interface/use-inject'

import { handleLoadUserInvite } from '../interface/handle-load-user-invite'
import { UserInvitesStore } from '../interface/user-invites.store'

import { InviteNewUserForm } from './invite-new-user.form'
import { InvitesList } from './invites-list'

export const UserInvitesContent = observer(() => {
  const { invitesStore } = useInject({
    invitesStore: UserInvitesStore,
  })

  useInitialLoad(() => {
    handleLoadUserInvite()
  })

  return (
    <div>
      <InviteNewUserForm
        isDisabled={!invitesStore.canInvite}
        onSubmit={(newInvite) => {
          invitesStore.addInvite(newInvite)
        }}
      />

      <Swap
        is={invitesStore.state.isLoading}
        isSlot={
          <div className="flex justify-center w-full">
            <Spinner />
          </div>
        }
      >
        {!!invitesStore.state.data && (
          <InvitesList
            invites={invitesStore.state.data.invites}
            maxInvites={invitesStore.state.data.maxInvites}
          />
        )}
      </Swap>
    </div>
  )
})
