import { getBlock } from '@repo/i18n'
import { useCallback } from 'react'

import { type UserInviteEntity } from '@wp/server/types'
import { Icon, Swap } from '@wp/ui-kit'
import { createConfirm } from '@wp-client/confirm'

import { handleRevokeInvite } from '../interface/handle-revoke-invite'

type Props = {
  maxInvites: number
  invites: UserInviteEntity[]
}

export function InvitesList({ maxInvites, invites }: Props) {
  const t = getBlock('userInvites')

  const handleRevoke = useCallback(async (inviteUuid: string, email: string) => {
    const confirmController = createConfirm({
      content: t('remove.sure', {
        email,
      }),
      applyButtonType: 'danger',
    })

    if ((await confirmController.handleOpen()) === 'cancel') {
      confirmController.handleClose()
      return
    }

    confirmController.setIsLoading(true)

    await handleRevokeInvite(inviteUuid)

    confirmController.handleClose()
  }, [])

  return (
    <>
      <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
        {invites.length} / {maxInvites} invites used
      </div>

      <Swap
        is={invites.length === 0}
        isSlot={
          <div className="flex items-center justify-center flex-col gap-2 py-6 text-gray-500 dark:text-gray-400">
            <Icon name="Mail" className="opacity-60 " size="lg"/>
            <p className="text-sm">{t('noInvites')}</p>
          </div>
        }
      >
        <div className="space-y-2">
          {invites.map((invite) => (
            <div
              key={invite.public_uuid}
              className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded-md"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                {invite.invite_status}
                <span className="text-sm text-gray-800 dark:text-gray-200 truncate">
                  {invite.email}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                  {invite.invite_status}
                </span>
              </div>

              {invite.invite_status === 'send' && (
                <button
                  onClick={() => handleRevoke(invite.public_uuid, invite.email)}
                  className="text-xs text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300"
                >
                  {t('revoke')}
                </button>
              )}
            </div>
          ))}
        </div>
      </Swap>
    </>
  )
}
