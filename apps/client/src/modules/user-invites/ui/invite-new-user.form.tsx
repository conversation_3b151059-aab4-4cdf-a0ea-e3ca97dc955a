import { getBlock } from '@repo/i18n'
import { isErr } from '@repo/result'
import { useState } from 'react'

import { Button, Form, HelperBlock, Input } from '@wp/ui-kit'

import { handleInviteNewUser } from '../interface/handle-invite-new-user'

import type { UserInviteEntity } from '@wp/server/types'

type Props = {
  isDisabled: boolean
  onSubmit: (invite: UserInviteEntity) => void
}

export function InviteNewUserForm({ onSubmit, isDisabled }: Props) {
  const t = getBlock('userInvites')

  const [email, setEmail] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSetEmail = (email: string) => {
    setEmail(email)
    setError('')
  }

  const validateEmail = (email: string): boolean => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  }

  const handleSubmit = async () => {
    if (isLoading) return
    if (!email.trim()) {
      setError(t('error.email'))
      return
    }

    if (!validateEmail(email)) {
      setError(t('error.validEmail'))
      return
    }

    setError('')
    setIsLoading(true)
    const res = await handleInviteNewUser(email)
    setIsLoading(false)
    if (isErr(res)) {
      setError(t('error.failed'))
      return
    }
    onSubmit(res.data.invite)
  }

  return (
    <Form onSubmit={handleSubmit} className="w-full">
      <div className="relative">
        <HelperBlock error={error}>
          <div className="flex gap-2">
            <Input
              autoFocus
              type="email"
              name="email"
              value={email}
              disabled={isDisabled || isLoading}
              onChange={handleSetEmail}
              placeholder={t('placeholder')}
            />
            <Button
              widthType="none"
              type="submit"
              iconLeft="Send"
              isLoading={isLoading}
              disabled={isDisabled}
            >
              <span className="text-nowrap">{t('send')}</span>
            </Button>
          </div>
        </HelperBlock>
      </div>
    </Form>
  )
}
