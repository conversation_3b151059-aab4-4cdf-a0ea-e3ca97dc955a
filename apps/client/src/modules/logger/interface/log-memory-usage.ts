export function logMemoryUsage(label: string) {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const used = process.memoryUsage().heapUsed / 1024 / 1024
    console.log(`Memory usage (${label}): ${Math.round(used * 100) / 100} MB`)
    // @ts-expect-error
  } else if (window.performance.memory) {
    // @ts-expect-error
    const used = window.performance.memory.usedJSHeapSize / 1024 / 1024
    console.log(`Memory usage (${label}): ${Math.round(used * 100) / 100} MB`)
  }
}
