import { Inject, observe } from '@repo/service'

import { type ViewModeValueObject } from '@wp-client/map/core/view-mode.value-object'

import { ViewModeStore } from './view-mode.store'

export function bindViewModeChanges(cb: (mode: ViewModeValueObject) => void) {
  const viewModeStore = Inject(ViewModeStore)

  cb(viewModeStore.viewMode)

  const remove = observe(viewModeStore, (ev) => {
    if (ev.type !== 'update') return
    if (ev.name !== 'viewMode') return
    cb(ev.newValue)
  })
  return remove
}
