import L from 'leaflet'

import { Scaler } from './scaler'

import './small-scale.css'

type TOptions = Partial<{
  position: 'bottomright'
  maxWidth: number
  isMeters: boolean
  langs: {
    main: string
    sub: string
  }
  updateWhenIdle: boolean
}>

const SmallScale = L.Control.extend<{
  _map?: L.Map
  options: TOptions
  _scaler?: Scaler
  _scaleContainer?: HTMLDivElement
  _leftValue?: HTMLDivElement
  _middleValue?: HTMLDivElement
  _rightValue?: HTMLDivElement
  onAdd: (map: L.Map) => HTMLDivElement
  onRemove: (map: L.Map) => void
  _addScales: (classPrefix: string, node: HTMLDivElement) => void
  _update: () => void
}>({
  options: {
    position: 'bottomright',
    maxWidth: 150,
    isMeters: true,
    langs: {
      main: 'km',
      sub: 'm',
    },
    updateWhenIdle: false,
  },

  onAdd(map: L.Map) {
    this._map = map
    this._scaler = new Scaler({
      maxWidth: this.options.maxWidth ?? 150,
      isMeters: this.options.isMeters ?? true,
    })
    const scaleClass = 'leaflet-control-small-scale'
    const scaleControllerEl = L.DomUtil.create('div', scaleClass)
    this._scaleContainer = scaleControllerEl

    const options = this.options

    const ruller = L.DomUtil.create(
      'div',
      `${scaleClass}-ruler`,
      scaleControllerEl,
    )
    L.DomUtil.create(
      'div',
      `${scaleClass}-ruler-block ${scaleClass}-lower-first-piece`,
      ruller,
    )
    L.DomUtil.create(
      'div',
      `${scaleClass}-ruler-block ${scaleClass}-lower-second-piece`,
      ruller,
    )

    this._addScales(scaleClass, scaleControllerEl)
    map.on(options.updateWhenIdle ? 'moveend' : 'move', this._update, this)
    map.whenReady(this._update, this)

    return scaleControllerEl
  },

  onRemove(map: L.Map) {
    const method = this.options.updateWhenIdle ? 'moveend' : 'move'
    map.off(method, this._update, this)
    this._scaler = undefined
  },

  _addScales(classPrefix: string, node: HTMLDivElement) {
    const basicClass = `${classPrefix}-label`
    this._scaleContainer = L.DomUtil.create(
      'div',
      `${classPrefix}-label-div`,
      node,
    )
    this._leftValue = L.DomUtil.create('div', basicClass, this._scaleContainer)
    this._middleValue = L.DomUtil.create(
      'div',
      `${basicClass}  ${classPrefix}-middle-number`,
      this._scaleContainer,
    )
    this._rightValue = L.DomUtil.create(
      'div',
      `${basicClass}  ${classPrefix}-right-number`,
      this._scaleContainer,
    )
  },

  _update() {
    !!this._scaler && (this._scaler.options.isMeters = this.options.isMeters!)
    const scales = this._map
      ? this._scaler?.calculateScales(this._map)
      : undefined
    if (!scales) {
      return
    }
    const lang = this.options.langs ? this.options.langs[scales.labelType] : 'm'

    if (this._scaleContainer)
      this._scaleContainer.style.width = `${scales.width}px`
    if (this._leftValue)
      this._leftValue.innerHTML = `${scales.leftValue} ${lang}`
    if (this._middleValue)
      this._middleValue.innerHTML = String(scales.middleValue)
    if (this._rightValue) this._rightValue.innerHTML = String(scales.rightValue)
  },
})

export function smallScale(options?: TOptions) {
  return new SmallScale(options)
}
