import { Inject } from '@repo/service'

import { type EntityWithSync } from '@wp-client/sync-engine/core/sync-metadata'

import { SyncEngine } from '../../basic/sync-engine'

import { queue } from './queue'

import type { Table } from 'dexie'

export const handleDeleteNode = queue('delete-node', async function <
  T,
>({ table, id }: { table: Table<EntityWithSync<T>>; id: string }): Promise<boolean> {
  const existing = await table.get(id)
  if (!existing) return false

  await table.where({ id }).modify({
    // @ts-expect-error
    version: existing.version + 1,
    lastModified: new Date(),
    synced: false,
    deleted: true,
  })

  const syncEngine = Inject(SyncEngine)
  syncEngine.scheduleSyncDebounced()
  return true
})
