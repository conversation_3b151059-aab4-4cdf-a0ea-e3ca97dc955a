import { Inject } from '@repo/service'

import { PolylinesStore } from '@wp-client/map-polyline/interface/polylines.store'

import { SyncEngine } from '../basic/sync-engine'

import { handleAddNode } from './actions/handle-add-node'
import { handleDeleteNode } from './actions/handle-delete-node'
import { handleUpdateNode } from './actions/handle-update-node'
import { initObserver } from './actions/init-observer'

export class LegsSyncProvider {
  constructor(
    private readonly syncEngine = Inject(SyncEngine),
    private readonly polylinesStore = Inject(PolylinesStore),
  ) {}

  private getTable() {
    return this.syncEngine.db.legs
  }

  initStoreToDb(): void {
    initObserver({
      key: 'legs',
      status: this.polylinesStore.status,
      map: this.polylinesStore.legsMap,
      onAdd: async (data) =>
        handleAddNode({
          id: data.id,
          table: this.getTable(),
          node: data,
        }),
      onUpdate: async (id, data) =>
        handleUpdateNode({
          node: data,
          id,
          table: this.getTable(),
          storedNode: this.polylinesStore.getLegById(id),
        }),
      onDelete: async (id) => this.onLegDelete(id),
    })
  }

  async onLegDelete(id: string) {
    await handleDeleteNode({
      table: this.getTable(),
      id,
    })
  }
}
