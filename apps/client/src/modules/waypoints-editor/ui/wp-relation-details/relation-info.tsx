import { Icon } from '@wp/ui-kit'
import { getDistanceTitle } from '@wp-client/unit-measure/interface/get-distance-title'
import { getDurationTitle } from '@wp-client/unit-measure/interface/get-duration-title'

type Props = {
  duration: number
  distance: number
}

export function RelationInfo({ duration, distance }: Props) {
  return (
    <div className="text-xs flex gap-1 flex-col text-gray-700 justify-center dark:text-gray-300">
      <div className="flex gap-1 items-center">
        <Icon name="Clock" size="xxxs" />
        {getDurationTitle(duration)}
      </div>
      <div className="flex gap-1 items-center">
        <Icon name="Route" size="xxxs" /> {getDistanceTitle(distance)}
      </div>
    </div>
  )
}
