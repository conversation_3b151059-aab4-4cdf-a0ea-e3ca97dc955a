.pulse-point {
  transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.pulse-point::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.5);
  animation: pulse 3s cubic-bezier(0.25, 0.1, 0.25, 1) infinite; /* Slower, more fluid animation */
  z-index: -1;
  pointer-events: none;
  will-change: transform, opacity; /* Performance optimization */
}
.pulse-point-small::after {
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
}

.pulse-point.active {
  box-shadow: 0 0 10px rgba(154, 154, 154, 0.35);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.1;
  }
  25% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 0.1;
  }
}

/* Add reduced motion preference for accessibility */
@media (prefers-reduced-motion: reduce) {
  .pulse-point::after {
    animation: none;
    opacity: 0.5;
  }
}
