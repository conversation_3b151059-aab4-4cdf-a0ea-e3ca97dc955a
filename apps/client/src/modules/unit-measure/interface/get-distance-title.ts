import { getBlock } from '@repo/i18n'
import { Inject } from '@repo/service'

import { getNumber } from '@wp-client/system/get-number'
import { type DistanceUnitValueObject } from '@wp-client/user-settings/core/distance-unit.value-object'
import { UserSettingsStore } from '@wp-client/user-settings/interface/user-settings.store'

const DISTANCE_K: Record<DistanceUnitValueObject, number> = {
  km: 1,
  mile: 0.621371,
}

export function getDistanceTitle(val: number): string {
  const t = getBlock('map.distance.scales')
  const store = Inject(UserSettingsStore)
  const distanceUnit = store.settings.distanceUnit

  if (!val) return '0'

  const correctVal = val * DISTANCE_K[distanceUnit]

  if (distanceUnit === 'km') {
    return correctVal > 1000
      ? `${getNumber(correctVal / 1000, 'distance')} ${t('km')}`
      : `${getNumber(correctVal, 'distance')} ${t('meter')}`
  }
  return correctVal > 1000
    ? `${getNumber(correctVal / 1000, 'distance')} ${t('mile')}`
    : `${getNumber(correctVal * 5.28, 'distance')} ${t('ft')}`
}
