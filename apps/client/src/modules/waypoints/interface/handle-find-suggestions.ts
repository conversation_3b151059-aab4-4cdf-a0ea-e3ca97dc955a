import { isErr, resultOk } from '@repo/result'
import { Inject } from '@repo/service'

import { getPointsByAddressAdapter } from '@wp-client/geocoder/infra/get-points-by-address.adapter'
import { MapLayerApi } from '@wp-client/map/interface/map-layer-api'

import { SearchSuggestionsStore } from './search-suggestions.store'

export async function handleFindSuggestions(placeName: string) {
  console.log('FindPlace', placeName)
  const store = Inject(SearchSuggestionsStore)
  const mapApi = Inject(MapLayerApi)

  store.suggestionsState.start()
  const res = await getPointsByAddressAdapter({
    q: placeName,
    center: mapApi.map.getCenter(),
  })

  if (placeName !== store.searchValue) return

  if (isErr(res)) {
    // TODO: add handle errors
    store.suggestionsState.finish()
    return
  }

  store.suggestionsState.setResult(
    resultOk(
      res.data.points.map((point) => {
        return {
          name: point.address.formatted || point.address.full,
          latLng: point.latLng,
        }
      }),
    ),
  )

  store.suggestionsState.finish()
}
