import { DataState, Store } from '@repo/service'

import { type LatLngValueObject } from '@wp/geo-metry'

type Place = {
  name: string
  latLng: LatLngValueObject
}

@Store()
export class SearchSuggestionsStore {
  suggestionsState = new DataState<Place[]>({
    isLoading: false,
  })

  timerId?: ReturnType<typeof setTimeout>

  selectedPlace?: Place = undefined

  searchValue?: string = undefined

  setSelectedPlace(place: Place): void {
    this.selectedPlace = place
  }

  setSearchValue(value: string): void {
    this.searchValue = value
  }

  setTimerId(timerId?: ReturnType<typeof setTimeout>): void {
    this.timerId = timerId
  }
}
