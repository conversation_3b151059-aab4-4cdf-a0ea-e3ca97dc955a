import L, { type LayerGroup, type Polyline } from 'leaflet'

import { type PathValueObject } from '@wp/geo-metry'

export function byAirplane(path: PathValueObject, polyLayer: LayerGroup) {
  const lines: Polyline[] = [
    L.polyline(path, {
      color: '#fafafa',
      weight: 6,
      opacity: 0.2,
      interactive: false,
    }).addTo(polyLayer),
    <PERSON><PERSON>polyline(path, {
      color: '#000',
      opacity: 0.2,
      weight: 4,
      interactive: false,
    }).addTo(polyLayer),
    <PERSON><PERSON>polyline(path, {
      color: '#2471bd',
      weight: 2,
      // opacity: 0.7,
      // dashArray: '10,6',
      interactive: false,
    }).addTo(polyLayer),
  ]
  return lines
}
