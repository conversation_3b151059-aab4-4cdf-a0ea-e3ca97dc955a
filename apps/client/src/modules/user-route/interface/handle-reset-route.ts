import { getBlock } from '@repo/i18n'
import { Inject } from '@repo/service'

import { createConfirm } from '@wp-client/confirm'
import { PolylinesStore } from '@wp-client/map-polyline/interface/polylines.store'
import { WpHistory } from '@wp-client/waypoint-history/interface/wp-history'
import { WaypointsStore } from '@wp-client/waypoints/interface/waypoints.store'

import { RoutesStore } from './routes.store'

export async function handleResetRoute() {
  const t = getBlock('userRoute')
  const confirmController = createConfirm({
    content: t('reset.sure'),
    applyButtonType: 'danger',
  })

  if ((await confirmController.handleOpen()) === 'cancel') {
    confirmController.handleClose()
    return
  }

  const wpHistory = Inject(WpHistory)
  const polyStore = Inject(PolylinesStore)
  const wpStore = Inject(WaypointsStore)
  const routeStore = Inject(RoutesStore)

  routeStore.resetCurrent()
  wpHistory.reset()
  polyStore.reset()
  wpStore.reset()
  confirmController.handleClose()
}
