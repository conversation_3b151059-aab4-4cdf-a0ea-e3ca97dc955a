import { observer } from '@repo/service'

import { Button } from '@wp/ui-kit'
import { useInject } from '@wp-client/service/interface/use-inject'

import { handleChangeLang } from '../interface/handle-change-lang'
import { UserSettingsStore } from '../interface/user-settings.store'

export const Language = observer(() => {
  const { store } = useInject({
    store: UserSettingsStore,
  })

  const current = store.settings.language

  return (
    <div className="flex items-center gap-2">
      <Button
        key="en"
        heightType="xs"
        onClick={() => {
          handleChangeLang('en-US')
        }}
        color={current === 'en-US' ? 'toggle-active' : 'toggle'}
      >
        English
      </Button>
      <Button
        key="ru"
        heightType="xs"
        onClick={() => {
          handleChangeLang('ru-RU')
        }}
        color={current === 'ru-RU' ? 'toggle-active' : 'toggle'}
      >
        Русский
      </Button>
    </div>
  )
})
