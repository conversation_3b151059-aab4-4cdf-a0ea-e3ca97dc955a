import { Inject } from '@repo/service'
import { type ReactNode } from 'react'

import { type ModalButtonType } from '@wp/ui-kit'

import { ConfirmStore } from './confirm.store'

export function createConfirm({
  content,
  description,
  applyButtonType,
}: {
  content: ReactNode
  description?: ReactNode
  applyButtonType?: ModalButtonType
}) {
  const store = Inject(ConfirmStore)

  const id = String(Date.now())
  return {
    setIsLoading: store.setIsLoading,
    handleOpen: (): Promise<'apply' | 'cancel'> => {
      return new Promise((resolve) => {
        store.pushItem({
          id,
          content,
          description,
          type: 'info',
          applyButtonType,
          onConfirm() {
            resolve('apply')
          },
          onClose() {
            store.removeItem(id)
            resolve('cancel')
          },
        })
      })
    },
    handleClose: () => {
      store.setIsLoading(false)
      store.removeItem(id)
    },
  }
}
