import { Inject, LocalStorageItem } from '@repo/service'

import { Routes } from '@wp-client/app-route/interface/routes'
import { HistoryService } from '@wp-client/navigation/interface/history.service'
import { API_URL } from '@wp-client/server-transport/infra/api-url'
import { UserStore } from '@wp-client/user/interface/user.store'

export async function handleLogout() {
  console.log('---handle logout')
  LocalStorageItem.clearAll()
  const userStore = Inject(UserStore)
  userStore.userMeta.reset()
  try {
    await fetch(`${API_URL}/auth/logout`, {
      method: 'POST',
      credentials: 'include',
      mode: 'cors',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Origin: window.location.origin,
      },
      body: JSON.stringify({}),
    })
  } catch (e) {
    console.error('Logout error:', e)
  }

  document.cookie = ''

  const history = Inject(HistoryService)
  history.push(Routes.auth.signIn.navigate())
  window.location.reload()
}
