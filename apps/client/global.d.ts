import { type MessageKeys, type NestedKeyOf } from '@repo/i18n'

type App = typeof import('@wp-client/app/i18n/app.en-us.json')
type Auth = typeof import('@wp-client/auth/i18n/auth.en-us.json')
type Map = typeof import('@wp-client/map/i18n/map.en-us.json')
type Settings = typeof import('@wp-client/settings/i18n/settings.en-us.json')
type Waypoints =
  typeof import('@wp-client/waypoints-editor/i18n/waypoints.en-us.json')
type UserRoute =
  typeof import('@wp-client/user-route/i18n/user-route.en-us.json')
type UserVehicle =
  typeof import('@wp-client/user-vehicle/i18n/user-vehicle.en-us.json')
type UserInvites =
  typeof import('@wp-client/user-invites/i18n/user-invites.en-us.json')


declare global {
  // Use type safe message keys with `next-intl`
  interface IntlMessages
    extends App,
      Auth,
      Map,
      Settings,
      Waypoints,
      UserRoute,
      UserInvites,
      UserVehicle {}

  type IntlMessageKeys = MessageKeys<IntlMessages, NestedKeyOf<IntlMessages>>
}
