import { isPointInPoly } from './is-point-in-poly'
import { polylineBypass } from './polyline-bypass'
import { type MultiPolygon, type Point, type Polygon } from './types'

export function isPointInPolyBypass(
  point: Point,
  polies: MultiPolygon | Polygon,
): boolean {
  let ret = false
  polylineBypass(polies, (pols: Polygon) => {
    const isInside = isPointInPoly(point, pols)
    ret = isInside
    return !ret
  })
  return ret
}
