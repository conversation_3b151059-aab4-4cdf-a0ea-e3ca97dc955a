import { type LatLngValueObject } from '@wp/geo-metry'

import type { GeoDirectionSegmentValueObject } from '@wp-modules/geo/core/geo-direction-segment.input'

export function getCoordinatesFromSegment(
  segments: GeoDirectionSegmentValueObject[],
): LatLngValueObject[] {
  return segments.reduce<LatLngValueObject[]>((acc, segment) => {
    acc.push(segment.from)
    acc.push(segment.to)
    return acc
  }, [])
}
