import { type Result, resultErr, resultOk } from '@repo/result'
import { nanoid } from 'nanoid'

import { createLogger } from '@wp-modules/logger'

import { getCoordinatesFromSegment } from '../get-coordinates-from-segment'
import { getDataAdapter } from '../get-data.adapter'
import { polylineToSegmentsPolyline } from '../polyline-to-segments-polyline'

import type { DirectionLegValueObject } from '@wp-modules/geo/core/direction-leg.value-object'
import type { DirectionRelationValueObject } from '@wp-modules/geo/core/direction-relation.value-object'
import type { GeoDirectionSegmentValueObject } from '@wp-modules/geo/core/geo-direction-segment.input'
import type { GetDirectionErrors } from '@wp-modules/geo/core/get-direction.errors'
import type { TransportModeValueObject } from '@wp-modules/geo/core/transport-mode.value-object'

type RouteResponse = {
  code: string
  routes: {
    distance: number
    duration: number
    geometry: string
    legs: {
      steps: unknown
      summary: string
      weight: number
      duration: number
      distance: number
    }[]
  }[]
}

const apiDirectionsUrl = 'https://api.mapbox.com/directions/v5'

const routerModeMap: Partial<Record<TransportModeValueObject, string>> = {
  bike: 'mapbox/cycling',
  car: 'mapbox/driving-traffic',
  walk: 'mapbox/walking',
}

const logger = createLogger('mapbox.get-directions')

export async function getDirections({
  segments,
}: {
  segments: GeoDirectionSegmentValueObject[]
}): Promise<
  Result<{ directions: DirectionRelationValueObject[] }, GetDirectionErrors>
> {
  if (segments.length === 0) {
    return resultErr('no-enough-coordinates')
  }

  const mode = segments[0].mode
  const profile = routerModeMap[mode]

  if (!profile) {
    return resultErr('mode-unsupported')
  }

  try {
    const coordinates = getCoordinatesFromSegment(segments)

    // Format coordinates for OSRM
    const coordinateString = coordinates
      .map((coord) => `${coord.lng},${coord.lat}`)
      .join(';')

    const params = new URLSearchParams()
    params.append('access_token', process.env.MAPBOX_DIRECTION_KEY || '')
    params.append('geometries', 'polyline')
    params.append('language', 'en')
    params.append('overview', 'full')
    params.append('notifications', 'none')
    // params.append('alternatives', 'true') // TODO: add direction select
    // params.append('exclude', '')

    const url = `${apiDirectionsUrl}/${profile}/${coordinateString}?${params.toString()}`

    // logger.log('url', url)
    const response = await getDataAdapter<RouteResponse>(url)

    if (response?.code !== 'Ok') {
      logger.error('response', response)
    }

    if (!response?.routes.length) {
      return resultErr('no-routes')
    }

    const directions = response.routes.map((route) => {
      const polyToSegmentMap = polylineToSegmentsPolyline({
        decodedPath: route.geometry,
        segments,
      })

      const legs = segments.map((segment, index) => {
        // get first or last leg only
        const leg =
          index === 0 ? route.legs[0] : route.legs[route.legs.length - 1]

        const resLeg: DirectionLegValueObject = {
          id: `seg-${nanoid()}`,
          sId: segment.sId,
          wpId: segment.wpId,
          path: polyToSegmentMap[segment.sId].path,
          distance: leg.distance,
          duration: leg.duration,
        }
        return resLeg
      })

      return {
        distance: route.distance,
        duration: route.duration,
        legs,
      }
    })

    return resultOk({ directions })
  } catch (error) {
    logger.error('unexpected', error)
    return resultErr('unexpected')
  }
}
