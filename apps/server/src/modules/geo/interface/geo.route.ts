import { Router } from 'express'

import { protectedRoute } from '@wp-modules/service/protected-route'

import { handleDirection } from './handle-direction'
import { handleIpLocation } from './handle-ip-location'
import { handleSearchLatLng } from './handle-search-latlng'
import { handleSearchName } from './handle-search-name'

export const geoRoute = Router()

geoRoute.get('/geo/direction', protectedRoute, handleDirection)
geoRoute.get('/geo/search/latlng', protectedRoute, handleSearchLatLng)
geoRoute.get('/geo/search/name', protectedRoute, handleSearchName)
geoRoute.get('/geo/search/ip', protectedRoute, handleIpLocation)
