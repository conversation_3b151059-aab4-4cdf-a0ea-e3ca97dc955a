import { resultErr } from '@repo/result'
import { Validator } from '@repo/validator'

import { serverAnswer } from '@wp-api/server-answer'
import { createLogger } from '@wp-modules/logger'
import { requestValidate } from '@wp-modules/request/interface/request-validate'
import { TokenType } from '@wp-modules/token/core/token-type.value-object'

import { getDirections } from '../infra/mapbox/mapbox.get-directions'

import type { TransportModeValueObject } from '@wp-modules/geo/core/transport-mode.value-object'

const segmentScheme = Validator.scheme.object({
  wpId: Validator.scheme.string().max(256),
  sId: Validator.scheme.string().max(256),
  from: Validator.scheme.latLng(),
  to: Validator.scheme.latLng(),
  mode: Validator.scheme.oneOf<TransportModeValueObject>([
    'car',
    'walk',
    'bike',
    'plane',
    'ship',
    'none',
  ]),
})

const schema = Validator.scheme.object({
  segments: Validator.scheme.array(segmentScheme),
})

const logger = createLogger('handle-direction')

const isEnabled = true

export const handleDirection = requestValidate(
  { schema, logger, tokenType: TokenType.access },
  async (req, res) => {
    if (!isEnabled) {
      console.log('--- handleDirection disabled')
      return serverAnswer(res, resultErr('no-routes'))
    }

    const directionsRes = await getDirections({
      segments: req.values.segments,
    })

    return serverAnswer(res, directionsRes)
  },
)
