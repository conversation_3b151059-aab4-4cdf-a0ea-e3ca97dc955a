export const signInTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Login to Your Account</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333333;
      background-color: #f9f9f9;
      padding: 40px 20px;
    }
    .container {
      max-width: 600px;
      background: #ffffff;
      margin: auto;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      text-align: center;
    }
    a.btn {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 24px;
      background-color: #007bff;
      color: #ffffff;
      text-decoration: none;
      font-weight: bold;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    .btn:hover {
      background-color: #0069d9;
    }
    .btn:visited {
      background-color: #6c757d;
      color: #f0f0f0;
    }
    .footer {
      margin-top: 30px;
      font-size: 0.9em;
      color: #888888;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>Welcome Back!</h2>
    <p>Click the button below to log in to your account:</p>
    <a href="{{MAGIC_LINK}}" class="btn" target="_blank">Log in with Magic Link</a>
    <p class="footer">
      This link is valid for 15 minutes and can only be used once.<br>
      If you didn't request this, please ignore this email.
    </p>
  </div>
</body>
</html>

`
