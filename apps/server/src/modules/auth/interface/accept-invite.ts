import { resultOk } from '@repo/result'

import { serverAnswer } from '@wp-api/server-answer'
import { createLogger } from '@wp-modules/logger'
import { requestValidate } from '@wp-modules/request/interface/request-validate'

const logger = createLogger('accept-invite')

export const acceptInvite = requestValidate({ logger }, async (_req, res) => {
  return serverAnswer(res, resultOk('unauthorized'), 403)
})
