import { isErr, resultErr } from '@repo/result'
import {
  type ValidatorSchemeObject,
  type ValidatorSchemeType,
} from '@repo/validator'
import { handleValidate } from '@repo/validator'
import { type Request, type Response } from 'express'

import { serverAnswer } from '@wp-api/server-answer'
import { serverFail } from '@wp-api/server-fail'
import { serverInvalid } from '@wp-api/server-invalid'
import { type createLogger } from '@wp-modules/logger'
import { getInputParams } from '@wp-modules/service/get-input-params'
import { type TokenType } from '@wp-modules/token/core/token-type.value-object'

import { type AuthorizedRequest } from '../core/authorized-request'

type ValidateOptions<T extends ValidatorSchemeType> = {
  schema?: ValidatorSchemeObject<T>
  logger: ReturnType<typeof createLogger>
  tokenType?: TokenType
}

type RequestHandler<T extends ValidatorSchemeType> = (
  req: AuthorizedRequest<T>,
  res: Response,
) => Promise<unknown> | unknown

export function requestValidate<T extends ValidatorSchemeType>(
  options: ValidateOptions<T>,
  handler: RequestHandler<T>,
) {
  return async (req: Request, res: Response) => {
    const validatedReq = req as AuthorizedRequest<T>

    if (options.tokenType) {
      if (!validatedReq.token) {
        options.logger.error('token-not-exist-in-req')
        return serverAnswer(res, resultErr('unauthorized'), 403)
      }
      if (validatedReq.token.t !== options.tokenType) {
        options.logger.error('invalid-token-type', {
          tokenType: validatedReq.token.t,
          expected: options.tokenType,
        })
        return serverAnswer(res, resultErr('unauthorized'), 403)
      }
    }

    const method = req.method.toLowerCase()
    if (method !== 'post' && method !== 'get') {
      return serverFail(res, 'wrong-method')
    }

    let body: any = req.body
    if (method === 'get') {
      const inputRes = getInputParams('body', req.url)

      if (isErr(inputRes)) {
        options.logger.error('wrong-input-params', inputRes)
        return serverAnswer(res, inputRes)
      }

      body = inputRes.data
    }

    if (options.schema) {
      const valid = handleValidate(options.schema, body)

      if (!valid.isValid) {
        options.logger.error('invalid-data', {
          body,
          errors: valid.errors,
        })
        return serverInvalid(res, valid.errors)
      }
      // @ts-expect-error
      validatedReq.values = valid.values
    }

    try {
      const result = await handler(validatedReq, res)
      return result
    } catch (e) {
      options.logger.error('unexpected', e)
      return serverFail(res, 'unknown')
    }
  }
}
