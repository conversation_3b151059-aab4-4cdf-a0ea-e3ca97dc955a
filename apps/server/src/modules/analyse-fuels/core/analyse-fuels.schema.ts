import { Validator } from '@repo/validator'

import { type TransportModeValueObject } from '@wp-modules/geo/core/transport-mode.value-object'

import type { FuelTypeValueObject } from '@wp-modules/fuel/core/fuel-type.value-object'

const s = Validator.scheme

export const analyseFuelsSchema = s.object({
  waypoints: s.array(
    s.object({
      point: s.latLng(),
      path: s.string(),
      wpId: s.string().min(1).max(256),
      mode: s.oneOf<TransportModeValueObject>([
        'none',
        'car',
        'walk',
        'bike',
        'plane',
        'ship',
      ]),
    }),
  ),
  vehicles: s.array(
    s.object({
      publicUuid: s.string(),
      details: s.object({
        name: s.string(),
        type: s.oneOf<FuelTypeValueObject>([
          'gasoline',
          'diesel',
          'lpg',
          'electro',
        ]),
        capacity: s.number().min(0).max(100000),
        efficiency: s.number().min(0).max(100000),
      }),
    }),
  ),
  isRound: s.boolean().optional(),
  toCurrency: s.string().max(10).optional(),
})
