import type { Request } from 'express'

export function getBearerToken(req: Request): string | undefined {
  if (
    req.method === 'OPTIONS' &&
    'access-control-request-headers' in req.headers &&
    req.headers['access-control-request-headers']
  ) {
    const hasAuthInAccessControl = req.headers['access-control-request-headers']
      .split(',')
      .map((header: string) => header.trim().toLowerCase())
      .includes('authorization')
    if (hasAuthInAccessControl) {
      return
    }
  }

  const authorizationHeader =
    req.headers && 'Authorization' in req.headers
      ? 'Authorization'
      : 'authorization'

  const parts = ((req.headers[authorizationHeader] as string) || '').split(' ')
  if (parts.length == 2) {
    const scheme = parts[0]
    const credentials = parts[1]

    if (/^Bearer$/i.test(scheme)) {
      return credentials
    }
  } else {
    // throw new UnauthorizedError('credentials_bad_format', { message: 'Format is Authorization: Bearer [token]' });
  }
}
