import { type TransportModeValueObject } from '@wp-modules/geo/core/transport-mode.value-object'

import type { LatLngValueObject } from '@wp/geo-metry'

export type WaypointEntity = {
  public_uuid: string
  cid: string
  wp_details: {
    mode: TransportModeValueObject
    point: LatLngValueObject
    nextId?: string
    title: string
    description: string
  }
  created_at: Date
  updated_at: Date
}
