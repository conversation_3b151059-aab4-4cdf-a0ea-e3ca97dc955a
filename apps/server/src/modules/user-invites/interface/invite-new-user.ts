import { isErr, resultErr, resultOk } from '@repo/result'
import { <PERSON>ida<PERSON> } from '@repo/validator'
import { v4 as uuidv4 } from 'uuid'

import { inviteNewUserUseCase } from '@wp/storage'
import { serverAnswer } from '@wp-api/server-answer'
import { createLogger } from '@wp-modules/logger'
import { sendEmail } from '@wp-modules/mail-sender/interface/send-email'
import { requestValidate } from '@wp-modules/request/interface/request-validate'
import { TokenType } from '@wp-modules/token/core/token-type.value-object'
import { generateToken } from '@wp-modules/token/interface/rsa256/generate-token'

import { type UserInviteEntity } from '../core/user-invite.entity'
import { createInviteTemplate } from '../infra/create-invite-template'
import { InviteNewUserResultValueObject } from '@wp-modules/user-invites/core/invite-new-user.result.value-object'

const logger = createLogger('invite-new-user')

export const inviteNewUser = requestValidate(
  {
    logger,
    schema: Validator.scheme.object({
      email: Validator.scheme.string().email().max(1000),
    }),
  },
  async (req, res) => {
    const userUuid = req.context.userUuid

    const key = uuidv4()

    const inviteToken = generateToken({
      payload: {
        key,
      },
      type: TokenType.invite,
      expiresIn: '3d',
    })

    const result = await inviteNewUserUseCase({
      invitedByUserUuid: userUuid,
      inviteKey: key,
      email: req.values.email,
    })

    if (isErr(result)) {
      logger.error('invite-user', {
        userUuid,
        error: result.error,
      })
      if (result.error === 'invite-already-exist') {
        return serverAnswer(res, resultErr('invite-already-exist'))
      }
      if (result.error === 'user-already-exist') {
        return serverAnswer(res, resultErr('user-already-exist'))
      }
      return serverAnswer(res, resultErr('bad-data'))
    }

    const template = createInviteTemplate({
      token: inviteToken,
      username: result.data.username,
    })

    const sendRes = await sendEmail({
      subject: "You're Invited to Waypool",
      to: req.values.email,
      html: template,
    })

    if (isErr(sendRes)) {
      logger.error('send-email', sendRes)
      return serverAnswer(res, resultErr('failed'))
    }

    return serverAnswer(
      res,
      resultOk<InviteNewUserResultValueObject>({
        invite: result.data.invite,
      }),
    )
  },
)
