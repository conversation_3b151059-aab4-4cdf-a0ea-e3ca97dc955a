import cookieParser from 'cookie-parser'
import cookieSession from 'cookie-session'

import { cookieConfig } from './cookie-config'

import type express from 'express'

export function setupCookie(app: express.Application) {
  app.use(cookieParser(process.env.COOKIE_SECRET || 'secret'))

  app.use(
    cookieSession({
      ...cookieConfig,
      name: 'se',
      keys: ['t', 's'],
      secret: process.env.COOKIE_SECRET || 'secret',
    }),
  )

  app.use((req, res, next) => {
    next()
  })
}

// const ms = require('ms');
// const cookieSession = require('cookie-session');
// const cookieParser = require('cookie-parser')
//
// module.exports = function (context) {
//   const config = context.config;
//   const app = context.app;
//
//   app.set('trust proxy', 1) // trust first proxy
//   const usePath = `${config.apiPath}/auth/`;
//
//   app.use(cookieParser(context.signer.sha512(config.token.cookieKey + 's0lt')));
//
//   app.use(cookieSession({
//     name: 'refreshId',
//     keys: [config.token.cookieKey],
//     domain: config.server.domain,
//     maxAge: ms(config.token.cookieMaxAge),
//     path: usePath,
//     secure: config.server.secureRefreshCookie,
//     httpOnly: true,
//   }));
//
//   app.use((req, res, next) => {
//     const token = req.headers['x-dci'];
//     req.hwmeta.fingerprint = token ? context.signer.sha512(token + '') : 'empty dci';
//     if (req.path.indexOf(usePath) === 0) {
//       req.hwmeta.fingerprint2 = context.authController.createFingerprint2(req);
//     }
//     return next();
//   });
// }
