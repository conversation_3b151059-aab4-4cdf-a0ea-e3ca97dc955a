import { decodePolylineToPath } from '@wp/geo-metry'
import { type TransportModeValueObject } from '@wp-modules/geo/core/transport-mode.value-object'

import { getPoiList } from '../infra/get-poi-list'
import { getRadius } from '../infra/get-radius'

type Args = {
  path: string
  mode: TransportModeValueObject
}

export async function handleLoadPoiList({ path, mode }: Args) {
  const radius = getRadius(mode)

  const pathList = decodePolylineToPath(path)

  const poiListResult = await getPoiList({
    pathList,
    radius,
  })

  return poiListResult
}
