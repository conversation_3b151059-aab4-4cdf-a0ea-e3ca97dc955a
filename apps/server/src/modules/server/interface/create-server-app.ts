import express from 'express'
import helmet from 'helmet'

import { error } from '@wp-api/middleware/error'
import { notFound } from '@wp-api/middleware/not-found'
import { registerRoutes } from '@wp-modules/server/infra/register-routes'
import { withLcidLogger } from '@wp-modules/server/infra/with-lcid-logger'

import { setupCookie } from '../../cookie/interface/setup-cookie'
import { createContext } from '../infra/create-context'
import { createReqToken } from '../infra/create-req-token'
import { setupCompression } from '../infra/setup-compression'
import { setupCors } from '../infra/setup-cors'

function devDelay(_req: express.Request, _res: express.Response, next: express.NextFunction) {
  setTimeout(next, 1500)
}

/**
 * Creates and configures an Express application with all middleware and routes
 * @returns Configured Express application
 */
export function createServerApp() {
  const app = express()

  // Move CORS middleware to the very top
  setupCors(app)

  // Add AsyncLocalStorage middleware at application level
  app.use(withLcidLogger)

  setupCookie(app)

  app.use(createReqToken)
  app.use(createContext)

  setupCompression(app)

  app.use(
    helmet({
      crossOriginResourcePolicy: { policy: 'cross-origin' },
      crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
    }),
  )
  app.disable('x-powered-by')
  // app.use(bodyParser.urlencoded({ extended: true }))

  if (process.env.NODE_ENV === 'development') {
    app.use(devDelay)
  }

  // Register routes
  registerRoutes(app)

  // Error handling middleware
  app.use(notFound)
  app.use(error)

  return app
}
