import { resultErr } from '@repo/result'

import type { UseCaseResult } from './use-case-result'

type Fu<T, E, V extends unknown = undefined> = (
  ...args: any[]
) => UseCaseResult<T, E, V>

export function UseCase<T, E, V, F extends Fu<T, E, V> = Fu<T, E, V>>(
  f: F,
): (...args: Parameters<F>) => ReturnType<F> {
  // eslint-disable-next-line eslint-comments/no-restricted-disable
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  return async (...args: Parameters<F>) => {
    try {
      const res = await f(...args)
      return res
    } catch (e: unknown) {
      console.error('use-case', e)
      return resultErr('unexpected')
    }
  }
}
