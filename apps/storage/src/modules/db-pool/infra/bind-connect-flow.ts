import pino from 'pino'

import type { Pool, PoolClient } from 'pg'

const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  redact: ['password', 'secret', 'token'],
})

export function bindConnectFlow(pool: Pool) {
  pool.on('connect', (client: PoolClient) => {
    logger.debug('New client connected to database')

    const errorHandler = (err: Error) => {
      logger.error({ err }, 'Database client error')
      client.removeListener('error', errorHandler)
    }

    client.on('error', errorHandler)

    // Remove listener when client is released
    client.on('end', () => {
      client.removeListener('error', errorHandler)
    })
  })
}
