import { resultErr, resultOk } from '@repo/result'

import { UseCase } from '~/modules/query'
import { toDbRouteMapper } from '~/modules/route/core/to-db-route.mapper'
import { toDbVehicleMapper } from '~/modules/vehicle/core/to-db-vehicle.mapper'
import { toDbWaypointMapper } from '~/modules/waypoint/core/to-db-waypoint.mapper'

import { toDbUserMapper } from '../core/to-db-user.mapper'
import { UserErrors } from '../core/user.errors'
import { getUserByUuidAdapter } from '../infra/get-user-by-uuid.adapter'
import { getUserRoutesAdapter } from '../infra/get-user-routes.adapter'
import { getUserRoutesWaypointsAdapter } from '../infra/get-user-routes-waypoints.adapter'
import { getUserVehiclesAdapter } from '../infra/get-user-vehicles.adapter'

import type { DbRouteEntity } from '~/modules/route/core/db-route.entity'
import type { DbVehicleEntity } from '~/modules/vehicle/core/db-vehicle.entity'
import type { DbWaypointEntity } from '~/modules/waypoint/core/db-waypoint.entity'
import type { DbUserEntity } from '../core/db-user.entity'

type SuccessRoute = {
  route: DbRouteEntity
  waypoints: DbWaypointEntity[]
  route_vehicles: DbVehicleEntity[]
}

type Success = {
  user: DbUserEntity
  user_vehicles: DbVehicleEntity[]
  routes: SuccessRoute[]
}

export const getUserDetailsUseCase = UseCase(async (userUuid: string) => {
  // return all user + routes + waypoints
  const userResult = await getUserByUuidAdapter(userUuid)

  if (!userResult) return resultErr(UserErrors('notExist'))

  const user = toDbUserMapper(userResult)

  const vehiclesResult = await getUserVehiclesAdapter(userResult.pid)

  const routesResult = await getUserRoutesAdapter(userResult.pid)

  const waypointsResult = await getUserRoutesWaypointsAdapter(userResult.pid)

  const routeVehiclesMap: Record<string, DbVehicleEntity[]> = {}
  const userVehicles: DbVehicleEntity[] = []

  vehiclesResult.forEach((vehicle) => {
    if (vehicle.user_pid) {
      userVehicles.push(toDbVehicleMapper(vehicle))
      return
    }

    if (!vehicle.route_pid) return
    if (!routeVehiclesMap[vehicle.route_pid]) {
      routeVehiclesMap[vehicle.route_pid] = []
    }
    routeVehiclesMap[vehicle.route_pid].push(toDbVehicleMapper(vehicle))
  })

  const waypointsMap = waypointsResult.reduce<
    Record<string, DbWaypointEntity[]>
  >((acc, waypoint) => {
    if (!waypoint.route_pid) return acc

    acc[waypoint.route_pid] = acc[waypoint.route_pid] || []
    acc[waypoint.route_pid].push(toDbWaypointMapper(waypoint))
    return acc
  }, {})

  const routes = routesResult.map<SuccessRoute>((routeRow) => {
    return {
      route: toDbRouteMapper(routeRow),
      waypoints: waypointsMap[routeRow.pid] || [],
      route_vehicles: routeVehiclesMap[routeRow.pid] || [],
    }
  })

  return resultOk<Success>({
    user,
    user_vehicles: userVehicles,
    routes,
  })
})
