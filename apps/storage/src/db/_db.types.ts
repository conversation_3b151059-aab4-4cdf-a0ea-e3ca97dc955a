/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from 'kysely'

export type Generated<T> =
  T extends ColumnType<infer S, infer I, infer U>
    ? ColumnType<S, I | undefined, U>
    : ColumnType<T, T | undefined, T>

export type Int8 = ColumnType<
  string,
  bigint | number | string,
  bigint | number | string
>

export type Json = JsonValue

export type JsonArray = JsonValue[]

export type JsonObject = {
  [x: string]: JsonValue | undefined
}

export type JsonPrimitive = boolean | number | string | null

export type JsonValue = JsonArray | JsonObject | JsonPrimitive

export type Timestamp = ColumnType<Date, Date | string, Date | string>

export type _PrismaMigrations = {
  applied_steps_count: Generated<number>
  checksum: string
  finished_at: Timestamp | null
  id: string
  logs: string | null
  migration_name: string
  rolled_back_at: Timestamp | null
  started_at: Generated<Timestamp>
}

export type PoiPoiCategories = {
  display_name: string
  id: Generated<number>
  main_tag: string
  value: string
}

export type PoiPois = {
  category_id: number | null
  created_at: Generated<Timestamp | null>
  id: Generated<number>
  osm_id: string
}

export type Polylines = {
  archived_at: Timestamp | null
  cid: string
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  poly_details: Json
  public_uuid: Generated<string>
  waypoints_pid: Int8
}

export type Routes = {
  archived_at: Timestamp | null
  cid: string
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  public_uuid: Generated<string>
  route_details: Generated<Json>
  updated_at: Generated<Timestamp>
  user_vehicles: Generated<Json>
}

export type RouteVehicles = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  route_pid: Int8
  vehicle_pid: Int8
}

export type UserInvites = {
  accepted_user_pid: Int8 | null
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  email: Generated<string>
  invite_key: string
  invite_status: string
  invited_by_pid: Int8
  pid: Generated<Int8>
  public_uuid: Generated<string>
}

export type UserProviders = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  login_key: string
  pid: Generated<Int8>
  public_uuid: Generated<string>
  type: string
  user_pid: Int8
}

export type UserRoutes = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  role: string
  route_pid: Int8
  user_pid: Int8
}

export type Users = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  max_invites: number | null
  nickname: Generated<string>
  otp: Generated<Json>
  pid: Generated<Int8>
  public_uuid: Generated<string>
  updated_at: Generated<Timestamp>
}

export type UserVehicles = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  user_pid: Int8
  vehicle_pid: Int8
}

export type Vehicles = {
  archived_at: Timestamp | null
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  public_uuid: Generated<string>
  updated_at: Generated<Timestamp>
  vehicle_details: Json
}

export type Waypoints = {
  archived_at: Timestamp | null
  cid: string
  created_at: Generated<Timestamp>
  pid: Generated<Int8>
  public_uuid: Generated<string>
  route_pid: Int8
  updated_at: Generated<Timestamp>
  wp_details: Json
}

export type DB = {
  _prisma_migrations: _PrismaMigrations
  'poi.poi_categories': PoiPoiCategories
  'poi.pois': PoiPois
  polylines: Polylines
  route_vehicles: RouteVehicles
  routes: Routes
  user_invites: UserInvites
  user_providers: UserProviders
  user_routes: UserRoutes
  user_vehicles: UserVehicles
  users: Users
  vehicles: Vehicles
  waypoints: Waypoints
}
