model Vehicles {
  pid         BigInt @id @default(autoincrement())
  public_uuid String @unique @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  vehicle_details Json

  user_vehicles  UserVehicles[]
  route_vehicles RouteVehicles[]

  created_at  DateTime  @default(now())
  updated_at  DateTime  @default(now()) @updatedAt
  archived_at DateTime?

  @@map("vehicles")
}

model UserVehicles {
  pid BigInt @id @default(autoincrement())

  user_pid BigInt
  user     Users  @relation(fields: [user_pid], references: [pid])

  vehicle_pid BigInt
  vehicle     Vehicles @relation(fields: [vehicle_pid], references: [pid])

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("user_vehicles")
}

model RouteVehicles {
  pid BigInt @id @default(autoincrement())

  route_pid BigInt
  route     Routes @relation(fields: [route_pid], references: [pid])

  vehicle_pid BigInt
  vehicle     Vehicles @relation(fields: [vehicle_pid], references: [pid])

  created_at  DateTime  @default(now())
  archived_at DateTime?

  @@map("route_vehicles")
}
