import { getPortions } from './get-portions'
import { msToHumanReadable } from './ms-to-human-readable'

import type { TimeLoggerDetails } from './time-logger-details'

export class TimeLogger {
  private details: TimeLoggerDetails = {}

  start(): void {
    this.details = {}
    this.details.start = Date.now()
  }

  pick(title: string): void {
    const node = {
      value: Date.now(),
      title,
    }
    if (this.details.picks) {
      this.details.picks.push(node)
      return
    }
    this.details.picks = [node]
  }

  end(): { total: string; portions: string[] } {
    this.details.end = Date.now()
    const dx = this.details.end - (this.details.start || 0)
    return {
      total: msToHumanReadable(dx),
      portions: getPortions(this.details),
    }
  }
}
