import { msToHumanReadable } from './ms-to-human-readable'

import type { TimeLoggerDetails } from './time-logger-details'

export function getPortions(details: TimeLoggerDetails): string[] {
  const start = details.start || 0
  if (!details.picks || details.picks.length === 0) return []

  const res = details.picks.reduce<{ current: number; title: string[] }>(
    (acc, item) => {
      const dx = item.value - acc.current
      const dxTitle = `${item.title}: ${msToHumanReadable(dx)}`
      acc.title.push(dxTitle)
      // acc.title = acc.title ? `${acc.title}; ${dxTitle}` : dxTitle
      acc.current = item.value
      return acc
    },
    { current: start, title: [] },
  )

  return res.title
}
