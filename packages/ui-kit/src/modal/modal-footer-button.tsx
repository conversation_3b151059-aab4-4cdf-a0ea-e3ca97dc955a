import { type ReactNode } from 'react'

import { But<PERSON> } from '../button'
import { type ButtonColorType } from '../button/button-color-type'

export type ModalButtonType = 'confirm' | 'cancel' | 'danger'

type Props = {
  autofocus?: boolean
  disabled?: boolean
  isLoading?: boolean
  onClick: () => void
  type: ModalButtonType
  children: ReactNode
  'data-test-id'?: string
}

const colorMap: Record<ModalButtonType, ButtonColorType> = {
  cancel: 'white',
  confirm: 'primary',
  danger: 'error',
}

export function ModalFooterButton({
  onClick,
  autofocus,
  isLoading,
  disabled,
  type,
  children,
  'data-test-id': dataTrack,
}: Props) {
  const color = colorMap[type]
  return (
    <Button
      autofocus={autofocus}
      disabled={disabled}
      widthType="none"
      heightType="sm"
      color={color}
      onClick={onClick}
      isLoading={isLoading}
      data-test-id={dataTrack}
    >
      {children}
    </Button>
  )
}
