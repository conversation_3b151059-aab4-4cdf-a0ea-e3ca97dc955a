import { useMemo } from 'react'

import * as svgAssets from './assets-svg'
import { getIconSizeStyle } from './get-icon-size-style'

import type { AssetSizeType } from './types'

type SvgNames = keyof typeof svgAssets
export type IconName = SvgNames

type Props = {
  name: SvgNames
  className?: string
  size?: AssetSizeType
}

export function Icon({ name, className, size }: Props) {
  const IconComponent = svgAssets[name] || undefined
  if (!IconComponent) {
    console.error(`Icon name "${name}" not defined!`)
  }

  const style = useMemo(() => {
    return getIconSizeStyle(size)
  }, [size])

  return (
    <span
      className={`flex items-center justify-center ${className || ''}`}
      style={style}
      data-icon-name={name}
    >
      {!!IconComponent && <IconComponent />}
    </span>
  )
}
