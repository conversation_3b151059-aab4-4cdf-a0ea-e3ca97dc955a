import { useEscPress } from '../hooks/use-esc-press'
import { Icon, type IconName } from '../icons'
import { Modal, type ModalButtonType, ModalFooterButton } from '../modal'

import type { ReactNode } from 'react'

export type ConfirmationType = 'info'

type Props = {
  type?: ConfirmationType
  applyButtonType?: ModalButtonType
  title: ReactNode
  subTitle?: ReactNode
  isOpen: boolean
  isLoading?: boolean
  onClose: () => void
  onSubmit: () => void
}

const iconMap: Record<ConfirmationType, IconName> = {
  info: 'Info',
}

export function ConfirmationModal({
  title,
  type = 'info',
  subTitle,
  isOpen,
  onClose,
  isLoading,
  onSubmit,
  applyButtonType = 'danger',
}: Props) {
  const icon = iconMap[type]
  useEscPress({
    isOpen,
    onClose,
  })
  return (
    <Modal onClose={onClose} isOpen={isOpen}>
      <Modal.Header />
      <Modal.Body>
        <div className="flex flex-col items-center gap-4 relative mb-1">
          <div className=" p-2 flex items-center justify-center rounded-xl bg-gray-100 dark:bg-gray-500 text-gray-500 dark:text-gray-600">
            <Icon name={icon} size="lg" />
          </div>
          <div className="text-inter text-center">
            <div className="text-base font-normal">{title}</div>
            {!!subTitle && <div className="text-gray-500 mt-3">{subTitle}</div>}
          </div>
          <div className="flex justify-center gap-4">
            <ModalFooterButton
              autofocus
              onClick={onClose}
              type="cancel"
              disabled={isLoading}
            >
              Cancel
            </ModalFooterButton>
            <ModalFooterButton
              onClick={onSubmit}
              isLoading={isLoading}
              type={applyButtonType}
            >
              Confirm
            </ModalFooterButton>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  )
}
