import { useCallback } from 'react'

import type { ChangeEvent, ReactNode } from 'react'

type Props = {
  name?: string
  checked?: boolean
  onChange?: (checked: boolean) => void
  children?: ReactNode
}

export function Toggle({ name, checked, onChange, children }: Props) {
  const handleChange = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      const isChecked = event.currentTarget.checked
      onChange?.(isChecked)
    },
    [onChange],
  )

  return (
    <label className="inline-flex relative items-center cursor-pointer">
      <input
        type="checkbox"
        name={name}
        checked={checked}
        onChange={handleChange}
        className="sr-only peer"
      />
      <div className="flex-shrink-0 w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white peer-checked:after:border-gray-700 after:content-[''] after:absolute after:top-0.5 max-sm:after:top-[10px] after:left-[2px] after:bg-white dark:after:bg-gray-600 after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600" />
      {!!children && (
        <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
          {children}
        </span>
      )}
    </label>
  )
}
