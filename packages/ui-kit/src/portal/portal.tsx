import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

import type { ReactNode } from 'react'

type Props = {
  target?: string
  children: ReactNode
  isOpen?: boolean
}

function getTarget(target?: string) {
  const resultTarget = target ? document.querySelector(target)! : document.body

  return resultTarget
}

let portalCounter = 1

export function Portal({ children, target, isOpen = true }: Props) {
  const [isHydrated, setIsHydrated] = useState(false)
  const [counter] = useState(portalCounter)

  useEffect(() => {
    portalCounter++
    setIsHydrated(true)
    return () => {
      portalCounter--
    }
  }, [])

  if (!isHydrated) return <></>

  if (!isOpen) return <></>

  return (
    <BasePortal target={target} counter={counter}>
      {children}
    </BasePortal>
  )
}

function BasePortal({
  children,
  target,
  counter,
}: {
  target?: string
  children: ReactNode
  counter: number
}) {
  const targetElement = getTarget(target)

  if (process.env.NODE_ENV !== 'production' && !targetElement) {
    throw new Error(
      `Looks like target prop for Portal component is not correct, please check "${target}"`,
    )
  }

  const portal = createPortal(
    <div data-test-id="portal" data-portal={counter}>
      {children}
    </div>,
    document.body,
  )

  return portal as JSX.Element
}
