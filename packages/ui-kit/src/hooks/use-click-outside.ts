import { useCallback, useEffect, useRef } from 'react'

type Options = {
  skipPortal?: boolean
  isEnabled?: boolean
}

function checkOtherPortalClick(el: Element): boolean {
  const nextSibling = el.closest('[data-portal]')?.nextSibling
  if (!nextSibling) return false
  return true
}

export function useClickOutside(onClick: () => void, opt?: Options) {
  const ref = useRef<HTMLDivElement | null>(null)
  const refMounted = useRef(false)
  const refOpt = useRef(opt)
  refOpt.current = opt

  useEffect(() => {
    refMounted.current = true
  }, [])

  const onClickHandler = useCallback(
    (event: Event) => {
      if (refOpt.current?.isEnabled === false) return
      if (!onClick) return
      if (!refMounted.current) return
      const targetEl = event.target as Element
      if (!ref.current) return

      if (!document.body.contains(targetEl)) return

      // skip all inside portal
      if (opt?.skipPortal && targetEl.closest('[data-portal]')) return

      if (checkOtherPortalClick(targetEl)) {
        return
      }

      if (!ref.current.contains(targetEl)) {
        // onClick()
      }
    },
    [ref, onClick],
  )

  useEffect(() => {
    if (!onClick) return
    // We add this handler at the end of the JS stack
    // So ongoing click event doesn't trigger right away
    const timerId = setTimeout(() => {
      document.addEventListener('click', onClickHandler)
    }, 0)

    return () => {
      clearTimeout(timerId)
      document.removeEventListener('click', onClickHandler)
    }
  }, [onClickHandler])

  return {
    ref,
  }
}
