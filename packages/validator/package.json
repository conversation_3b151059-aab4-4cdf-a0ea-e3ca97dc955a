{"name": "@repo/validator", "version": "0.0.1", "license": "MIT", "private": true, "sideEffects": false, "source": "src/index.ts", "types": "src/index.ts", "main": "dist/index.mjs", "module": "dist/index.mjs", "scripts": {"check-types": "tsc", "dev": "tsup --watch", "build": "tsup", "test:unit": "repo-unit", "clean": "rimraf ./dist && rimraf .turbo", "clean:node_modules": "rimraf node_modules"}, "devDependencies": {"@repo/unit-test": "workspace:*", "@repo/ts-config": "workspace:*", "@repo/eslint-config": "workspace:*"}}